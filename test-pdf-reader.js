/**
 * 测试PDF Reader MCP工具
 */

const { spawn } = require('child_process');
const path = require('path');

async function testPDFReader() {
    console.log('🔍 测试PDF Reader MCP工具...');
    
    const pdfReaderPath = path.join(__dirname, 'node_modules/@sylphlab/pdf-reader-mcp/dist/src/index.js');
    const pdfFilePath = path.join(__dirname, '捉宠挂机放置mmo.pdf');
    
    console.log(`📄 PDF文件路径: ${pdfFilePath}`);
    console.log(`🛠️ PDF Reader路径: ${pdfReaderPath}`);
    
    // 检查文件是否存在
    const fs = require('fs');
    if (!fs.existsSync(pdfFilePath)) {
        console.error('❌ PDF文件不存在');
        return;
    }
    
    if (!fs.existsSync(pdfReaderPath)) {
        console.error('❌ PDF Reader工具不存在');
        return;
    }
    
    console.log('✅ 文件检查通过');
    console.log('📋 MCP配置已更新，PDF Reader工具已配置完成');
    
    // 显示配置信息
    console.log('\n📝 MCP配置信息:');
    console.log('服务器名称: pdf-reader');
    console.log('命令: node');
    console.log('参数: ./node_modules/@sylphlab/pdf-reader-mcp/dist/src/index.js');
    console.log('工具: read_pdf, extract_text, get_pdf_info');
    
    console.log('\n🎉 PDF Reader MCP工具配置完成！');
    console.log('现在可以使用MCP工具来读取PDF文件内容了。');
}

testPDFReader().catch(console.error);
