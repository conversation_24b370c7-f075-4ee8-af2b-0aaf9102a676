#!/usr/bin/env python3
"""
测试PDF MCP工具的脚本
"""

import sys
import os
import json
from pathlib import Path

# 添加pdf-mcp-server目录到Python路径
sys.path.insert(0, str(Path(__file__).parent / "pdf-mcp-server"))

try:
    from utils import PDFUtils
    from qpdf_tools import QPDFTools
    from pdftk_tools import PDFtkTools
except ImportError as e:
    print(f"❌ 导入错误: {e}")
    print("请确保pdf-mcp-server目录存在且包含必要的文件")
    sys.exit(1)

def test_pdf_tools():
    """测试PDF工具功能"""
    print("🔍 测试PDF MCP工具...")
    
    # 初始化工具
    utils = PDFUtils()
    qpdf = QPDFTools()
    pdftk = PDFtkTools()
    
    # 测试PDF文件路径
    pdf_file = "捉宠挂机放置mmo.pdf"
    pdf_path = Path(pdf_file)
    
    print(f"📄 目标PDF文件: {pdf_file}")
    
    # 检查文件是否存在
    if not pdf_path.exists():
        print(f"❌ PDF文件不存在: {pdf_path.absolute()}")
        return False
    
    print(f"✅ PDF文件存在: {pdf_path.absolute()}")
    print(f"📊 文件大小: {pdf_path.stat().st_size / 1024 / 1024:.2f} MB")
    
    # 测试工具状态
    print("\n🛠️ 测试工具状态...")
    
    # 测试QPDF
    try:
        qpdf_status = qpdf.check_qpdf_installation()
        print(f"QPDF状态: {qpdf_status}")
    except Exception as e:
        print(f"QPDF测试失败: {e}")
    
    # 测试PDFtk
    try:
        pdftk_status = pdftk.check_pdftk_installation()
        print(f"PDFtk状态: {pdftk_status}")
    except Exception as e:
        print(f"PDFtk测试失败: {e}")
    
    # 尝试获取PDF信息
    print(f"\n📋 尝试获取PDF信息...")
    try:
        # 使用QPDF获取信息
        info_result = qpdf.get_pdf_info(str(pdf_path))
        print("QPDF信息获取结果:")
        print(json.dumps(info_result, indent=2, ensure_ascii=False))
        
        if info_result.get("success"):
            data = info_result.get("data", {})
            if "pages" in data:
                print(f"📄 PDF页数: {data['pages']}")
            if "title" in data:
                print(f"📝 PDF标题: {data['title']}")
        
    except Exception as e:
        print(f"❌ 获取PDF信息失败: {e}")
    
    # 尝试使用PDFtk获取信息
    print(f"\n📋 尝试使用PDFtk获取PDF信息...")
    try:
        pdftk_info = pdftk.get_pdf_info(str(pdf_path))
        print("PDFtk信息获取结果:")
        print(json.dumps(pdftk_info, indent=2, ensure_ascii=False))
        
    except Exception as e:
        print(f"❌ PDFtk获取PDF信息失败: {e}")
    
    print("\n🎉 PDF工具测试完成!")
    return True

def main():
    """主函数"""
    print("=" * 60)
    print("PDF MCP工具测试")
    print("=" * 60)
    
    try:
        success = test_pdf_tools()
        if success:
            print("\n✅ 测试完成")
            print("\n💡 下一步:")
            print("1. 重启Augment以加载新的MCP配置")
            print("2. 在Augment中使用PDF工具读取游戏设计文档")
            print("3. 基于文档内容更新项目介绍")
        else:
            print("\n❌ 测试失败")
            
    except Exception as e:
        print(f"\n❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
