#!/usr/bin/env python3
"""
简单的PDF文件测试
"""

import os
from pathlib import Path

def test_pdf_file():
    """测试PDF文件基本信息"""
    pdf_file = "捉宠挂机放置mmo.pdf"
    pdf_path = Path(pdf_file)
    
    print(f"📄 测试PDF文件: {pdf_file}")
    print(f"📍 完整路径: {pdf_path.absolute()}")
    
    # 检查文件是否存在
    if not pdf_path.exists():
        print("❌ 文件不存在")
        return False
    
    print("✅ 文件存在")
    
    # 检查文件大小
    size = pdf_path.stat().st_size
    print(f"📊 文件大小: {size} 字节 ({size/1024/1024:.2f} MB)")
    
    # 检查文件扩展名
    print(f"📝 文件扩展名: {pdf_path.suffix}")
    
    # 尝试读取文件头部
    try:
        with open(pdf_path, 'rb') as f:
            header = f.read(10)
            print(f"📋 文件头部: {header}")
            
            # 检查是否是PDF文件
            if header.startswith(b'%PDF'):
                print("✅ 这是一个有效的PDF文件")
                return True
            else:
                print("❌ 这不是一个有效的PDF文件")
                return False
                
    except Exception as e:
        print(f"❌ 读取文件失败: {e}")
        return False

def main():
    print("=" * 50)
    print("简单PDF文件测试")
    print("=" * 50)
    
    success = test_pdf_file()
    
    if success:
        print("\n🎉 PDF文件测试通过!")
        print("\n💡 建议:")
        print("1. PDF文件格式正常")
        print("2. 可能需要安装QPDF或PDFtk工具")
        print("3. 或者PDF内容是图片格式，需要OCR处理")
    else:
        print("\n❌ PDF文件测试失败")
        print("\n💡 建议:")
        print("1. 检查PDF文件是否损坏")
        print("2. 尝试用其他PDF阅读器打开")
        print("3. 重新生成PDF文件")

if __name__ == "__main__":
    main()
