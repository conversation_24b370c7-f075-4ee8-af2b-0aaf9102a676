/**
 * 直接读取PDF文件内容的脚本
 */

const fs = require('fs');
const path = require('path');

async function readPDFContent() {
    const pdfPath = path.join(__dirname, '捉宠挂机放置mmo.pdf');
    
    console.log('🔍 开始读取PDF文件...');
    console.log(`📄 文件路径: ${pdfPath}`);
    
    // 检查文件是否存在
    if (!fs.existsSync(pdfPath)) {
        console.error('❌ PDF文件不存在');
        return;
    }
    
    // 获取文件信息
    const stats = fs.statSync(pdfPath);
    console.log(`📊 文件大小: ${(stats.size / 1024 / 1024).toFixed(2)} MB`);
    console.log(`📅 修改时间: ${stats.mtime.toLocaleString()}`);
    
    try {
        // 尝试使用pdf-parse库（如果安装了）
        let pdfParse;
        try {
            pdfParse = require('pdf-parse');
        } catch (e) {
            console.log('📦 pdf-parse库未安装，尝试安装...');
            const { execSync } = require('child_process');
            try {
                execSync('npm install pdf-parse', { stdio: 'inherit' });
                pdfParse = require('pdf-parse');
                console.log('✅ pdf-parse库安装成功');
            } catch (installError) {
                console.error('❌ 无法安装pdf-parse库:', installError.message);
                return;
            }
        }
        
        // 读取PDF文件
        const dataBuffer = fs.readFileSync(pdfPath);
        console.log('📖 正在解析PDF内容...');
        
        const data = await pdfParse(dataBuffer);
        
        console.log('\n📋 PDF文件信息:');
        console.log(`📄 总页数: ${data.numpages}`);
        console.log(`📝 文本长度: ${data.text.length} 字符`);
        
        // 显示前2000个字符的内容
        console.log('\n📖 PDF内容预览 (前2000字符):');
        console.log('=' * 50);
        console.log(data.text.substring(0, 2000));
        console.log('=' * 50);
        
        // 保存完整内容到文件
        const outputPath = path.join(__dirname, 'pdf-content-extracted.txt');
        fs.writeFileSync(outputPath, data.text, 'utf8');
        console.log(`\n💾 完整内容已保存到: ${outputPath}`);
        
        // 分析内容关键词
        console.log('\n🔍 内容关键词分析:');
        const keywords = ['捉宠', '挂机', '放置', '宠物', '战斗', '生产', '养成', '星露谷', '帕鲁'];
        keywords.forEach(keyword => {
            const count = (data.text.match(new RegExp(keyword, 'g')) || []).length;
            if (count > 0) {
                console.log(`  ${keyword}: ${count} 次`);
            }
        });
        
        return data.text;
        
    } catch (error) {
        console.error('❌ 读取PDF失败:', error.message);
        
        // 尝试使用@sylphlab/pdf-reader-mcp包
        console.log('\n🔄 尝试使用PDF Reader MCP包...');
        try {
            // 这里我们只能显示文件存在，无法直接调用MCP工具
            console.log('✅ PDF文件存在，可以通过MCP工具读取');
            console.log('💡 建议通过Augment的MCP工具界面使用read_pdf工具');
        } catch (mcpError) {
            console.error('❌ MCP工具调用失败:', mcpError.message);
        }
    }
}

// 运行脚本
readPDFContent().catch(console.error);
