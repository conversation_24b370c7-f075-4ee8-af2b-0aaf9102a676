{"mcpServers": {"context7": {"command": "npx", "args": ["@context7/mcp-server"], "env": {"CONTEXT7_API_KEY": "${CONTEXT7_API_KEY}", "NODE_ENV": "production"}, "description": "Context 7 MCP Server - Provides 2 tools for library documentation and code context", "tools": ["resolve-library-id", "get-library-docs"], "category": "documentation"}, "playwright": {"command": "npx", "args": ["@playwright/mcp-server"], "env": {"PLAYWRIGHT_HEADLESS": "true", "PLAYWRIGHT_TIMEOUT": "30000", "NODE_ENV": "production"}, "description": "Playwright MCP Server - Provides 24 tools for web automation and browser testing", "tools": ["browser_close", "browser_resize", "browser_console_messages", "browser_handle_dialog", "browser_evaluate", "browser_file_upload", "browser_install", "browser_press_key", "browser_type", "browser_navigate", "browser_navigate_back", "browser_navigate_forward", "browser_network_requests", "browser_take_screenshot", "browser_snapshot", "browser_click", "browser_drag", "browser_hover", "browser_select_option", "browser_tab_list", "browser_tab_new", "browser_tab_select", "browser_tab_close", "browser_wait_for"], "category": "automation"}, "cocos-creator": {"command": "node", "args": ["./extensions/cocos-mcp-server/dist/mcp-server.js"], "env": {"COCOS_PROJECT_PATH": "./", "MCP_SERVER_PORT": "3000", "NODE_ENV": "production", "DEBUG": "false"}, "description": "Cocos Creator MCP Server - Provides 49 tools for Cocos Creator development", "tools": ["scene_scene_management", "scene_scene_hierarchy", "scene_scene_execution_control", "scene_scene_state_management", "scene_scene_query_system", "node_node_query", "node_node_lifecycle", "node_node_transform", "node_node_hierarchy", "node_node_clipboard", "node_node_property_management", "node_node_array_management", "component_component_manage", "component_component_query", "component_set_component_property", "component_configure_click_event", "prefab_prefab_browse", "prefab_prefab_lifecycle", "prefab_prefab_instance", "prefab_prefab_edit", "project_project_manage", "project_project_build_system", "debug_debug_console", "debug_debug_logs", "debug_debug_system", "preferences_preferences_manage", "preferences_preferences_query", "preferences_preferences_backup", "server_server_information", "server_server_connectivity", "broadcast_broadcast_log_management", "broadcast_broadcast_listener_management", "sceneView_scene_view_gizmo_management", "sceneView_scene_view_mode_control", "sceneView_scene_view_icon_gizmo", "sceneView_scene_view_camera_control", "sceneView_scene_view_status_management", "referenceImage_reference_image_management", "referenceImage_reference_image_query", "referenceImage_reference_image_transform", "referenceImage_reference_image_display", "assetAdvanced_asset_manage", "assetAdvanced_asset_analyze", "assetAdvanced_asset_system", "assetAdvanced_asset_query", "assetAdvanced_asset_operations", "validation_validate_json_params", "validation_safe_string_value", "validation_format_mcp_request"], "category": "game-development"}, "pdf-reader-mcp": {"command": "npx", "args": ["@sylphlab/pdf-reader-mcp"], "name": "PDF Reader (npx)"}, "pdf-tools": {"command": "python", "args": ["./pdf-mcp-server/server.py"], "env": {"NODE_ENV": "production"}, "description": "Comprehensive PDF manipulation toolkit - Merge, split, encrypt, optimize PDFs through natural language commands", "tools": ["merge_pdfs", "split_pdf", "extract_pages", "rotate_pages", "encrypt_pdf", "decrypt_pdf", "optimize_pdf", "repair_pdf", "get_pdf_info", "download_pdf", "get_server_status"], "category": "document-processing"}}, "globalSettings": {"timeout": 30000, "retries": 3, "logLevel": "info", "enableDebug": false}, "metadata": {"version": "1.0.2", "description": "MCP服务器配置文件，包含Context 7、Playwright、Cocos Creator和PDF Reader工具", "created": "2025-01-01T00:00:00Z", "updated": "2025-08-03T09:00:00Z", "author": "Cocos Creator Project", "totalTools": 76, "categories": ["documentation", "automation", "game-development", "document-processing"]}}