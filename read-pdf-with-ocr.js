/**
 * 使用OCR读取图片格式PDF的脚本
 */

const fs = require('fs');
const path = require('path');

async function readPDFWithOCR() {
    const pdfPath = path.join(__dirname, '捉宠挂机放置mmo.pdf');
    
    console.log('🔍 尝试使用OCR读取PDF文件...');
    console.log(`📄 文件路径: ${pdfPath}`);
    
    try {
        // 尝试使用pdf2pic转换PDF为图片，然后OCR
        console.log('📦 安装必要的OCR依赖...');
        const { execSync } = require('child_process');
        
        try {
            // 安装pdf2pic和tesseract.js
            console.log('正在安装pdf2pic和tesseract.js...');
            execSync('npm install pdf2pic tesseract.js', { stdio: 'inherit' });
            console.log('✅ OCR依赖安装成功');
        } catch (installError) {
            console.error('❌ 无法安装OCR依赖:', installError.message);
            console.log('\n💡 替代方案建议:');
            console.log('1. 手动打开PDF文件查看内容');
            console.log('2. 将PDF转换为文本格式');
            console.log('3. 使用在线OCR工具');
            return;
        }
        
        const pdf2pic = require('pdf2pic');
        const Tesseract = require('tesseract.js');
        
        console.log('🖼️ 将PDF转换为图片...');
        
        // 配置pdf2pic
        const convert = pdf2pic.fromPath(pdfPath, {
            density: 300,           // 高分辨率
            saveFilename: "page",
            savePath: "./temp-pdf-images/",
            format: "png",
            width: 2000,
            height: 2000
        });
        
        // 创建临时目录
        const tempDir = path.join(__dirname, 'temp-pdf-images');
        if (!fs.existsSync(tempDir)) {
            fs.mkdirSync(tempDir, { recursive: true });
        }
        
        // 转换第一页
        const result = await convert(1);
        console.log('✅ PDF转图片成功');
        
        // 使用OCR识别文字
        console.log('🔍 正在进行OCR文字识别...');
        const { data: { text } } = await Tesseract.recognize(
            result.path,
            'chi_sim+eng', // 中文简体 + 英文
            {
                logger: m => {
                    if (m.status === 'recognizing text') {
                        console.log(`OCR进度: ${Math.round(m.progress * 100)}%`);
                    }
                }
            }
        );
        
        console.log('\n📖 OCR识别结果:');
        console.log('=' * 50);
        console.log(text);
        console.log('=' * 50);
        
        // 保存OCR结果
        const ocrOutputPath = path.join(__dirname, 'pdf-ocr-result.txt');
        fs.writeFileSync(ocrOutputPath, text, 'utf8');
        console.log(`\n💾 OCR结果已保存到: ${ocrOutputPath}`);
        
        // 清理临时文件
        try {
            fs.rmSync(tempDir, { recursive: true, force: true });
            console.log('🧹 临时文件已清理');
        } catch (cleanupError) {
            console.log('⚠️ 临时文件清理失败，请手动删除temp-pdf-images目录');
        }
        
        return text;
        
    } catch (error) {
        console.error('❌ OCR处理失败:', error.message);
        
        // 提供手动处理建议
        console.log('\n💡 手动处理建议:');
        console.log('1. 使用Adobe Reader或其他PDF阅读器打开文件');
        console.log('2. 复制文本内容并粘贴到文本文件');
        console.log('3. 或者告诉我PDF的主要内容，我可以帮您更新项目文档');
        
        return null;
    }
}

// 运行脚本
readPDFWithOCR().catch(console.error);
